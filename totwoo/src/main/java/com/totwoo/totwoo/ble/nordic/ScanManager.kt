package com.totwoo.totwoo.ble.nordic

import android.app.PendingIntent
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import com.blankj.utilcode.util.Utils
import com.totwoo.library.exception.DbException
import com.totwoo.library.util.Apputils
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.bean.LocalJewelryInfo
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.ble.BleUtils
import com.totwoo.totwoo.service.KeepAliveService
import com.totwoo.totwoo.utils.LocalJewelryDBHelper
import com.totwoo.totwoo.utils.PermissionUtil
import com.totwoo.totwoo.utils.PreferencesUtils
import no.nordicsemi.android.support.v18.scanner.*

/**
 * 扫描管理器 - 替代BluetoothWrapper的扫描功能
 *
 * 功能：
 * 1. 设备发现扫描 - 用于JewelryConnectActivity
 * 2. 重连扫描 - 扫描指定MAC地址设备
 * 3. OTA扫描 - 扫描OTA模式设备
 * 4. 保活扫描 - 间歇性扫描保持进程活跃
 */
object ScanManager {
    private const val TAG = "ScanManager"
    private const val SCAN_TIMEOUT = 20000L // 20秒超时
    private const val OTA_SCAN_TIMEOUT = 30000L // OTA扫描30秒超时


    private val scanner = BluetoothLeScannerCompat.getScanner()
    private val handler = Handler(Looper.getMainLooper())
    private var isScanning = false
    private var currentCallback: no.nordicsemi.android.support.v18.scanner.ScanCallback? = null
    private var timeoutRunnable: Runnable? = null
    private var currentScanType: ScanType? = null

    // 保活扫描已简化为PendingIntent扫描，移除传统间歇性扫描相关变量

    // 后台扫描相关变量
    private var backgroundScanPendingIntent: PendingIntent? = null
    private const val BLE_BACKGROUND_SCAN_REQUEST_CODE = 68

    // 扫描类型枚举
    enum class ScanType {
        DEVICE_DISCOVERY,    // 设备发现扫描
        ADDRESS_SCAN,        // 地址扫描（重连）
        OTA_SCAN,           // OTA扫描
        BACKGROUND_SCAN     // 后台扫描
    }

    // PendingIntent扫描相关 - 用于锁屏唤醒
    private var pendingIntentScan: PendingIntent? = null
    private var isPendingIntentScanActive = false
    private var pendingIntentScanReceiver: BroadcastReceiver? = null

    // PendingIntent扫描的Action
    private const val ACTION_BLE_SCAN_RESULT = "com.totwoo.totwoo.BLE_SCAN_RESULT"

    // 本地已绑定设备列表
    private var localJewelryInfos: List<LocalJewelryInfo>? = null

    // 扫描回调接口
    interface ScanCallback {
        fun onDeviceFound(device: BluetoothDevice, rssi: Int, scanRecord: ScanRecord?) {}
        fun onScanTimeout()
        fun onScanFailed(errorCode: Int)
        fun onOTADeviceFound(device: BluetoothDevice) {}
        fun onAddressDeviceFound(device: BluetoothDevice) {}
    }

    /**
     * 检查扫描冲突
     */
    private fun checkScanConflict(newScanType: ScanType): Boolean {
        if (isScanning) {
            LogUtils.w(TAG, "扫描冲突检测: 当前正在进行${currentScanType}扫描，无法启动${newScanType}扫描")
            return true
        }
        return false
    }

    /**
     * 1. 设备发现扫描
     * 用于JewelryConnectActivity中的新设备发现
     */
    fun startDeviceDiscoveryScan(callback: ScanCallback) {
        if (checkScanConflict(ScanType.DEVICE_DISCOVERY)) {
            callback.onScanFailed(-2) // 自定义错误码表示扫描冲突
            return
        }

        LogUtils.d(TAG, "开始设备发现扫描")

        // 获取本地已绑定设备列表
        try {
            localJewelryInfos = LocalJewelryDBHelper.getInstance().allBeans
        } catch (e: DbException) {
            LogUtils.e("获取本地设备列表失败", e)
            localJewelryInfos = emptyList()
        }

        val scanCallback = object : no.nordicsemi.android.support.v18.scanner.ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                // 单个结果处理（通常不会触发，因为设置了批量模式）
            }

            override fun onBatchScanResults(results: List<ScanResult>) {
                filterAndProcessDevices(results, callback)
            }

            override fun onScanFailed(errorCode: Int) {
                LogUtils.e(TAG, "设备发现扫描失败: $errorCode")
                stopScan()
                callback.onScanFailed(errorCode)
            }
        }

        val settings = ScanSettings.Builder()
            .setLegacy(true)
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setReportDelay(30) // 30ms批量报告，与原有保持一致
            .setUseHardwareBatchingIfSupported(false)
            .build()

        val filters = ArrayList<ScanFilter>() // 不设置过滤器，扫描所有设备

        startScanInternal(scanCallback, settings, filters, SCAN_TIMEOUT, ScanType.DEVICE_DISCOVERY) {
            isScanning = false
            currentScanType = null
            callback.onScanTimeout()
        }
    }

    /**
     * 2. 重连扫描 - 替代BluetoothWrapper.scanAddress()
     */
    fun startAddressScan(fromUser: Boolean = false,address: String, callback: ScanCallback) {
        if (checkScanConflict(ScanType.ADDRESS_SCAN)) {
            callback.onScanFailed(-2) // 自定义错误码表示扫描冲突
            return
        }

        if (TextUtils.isEmpty(address) || !android.bluetooth.BluetoothAdapter.checkBluetoothAddress(
                address
            )
        ) {
            LogUtils.e(TAG, "无效的设备地址: $address")
            isScanning = false
            callback.onScanFailed(-1)
            return
        }

        LogUtils.d(TAG, "开始地址扫描: $address")

        val scanCallback = object : no.nordicsemi.android.support.v18.scanner.ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                if (result.device.address == address) {
                    LogUtils.d(TAG, "找到目标设备: $address")
                    stopScan()
                    callback.onAddressDeviceFound(result.device)
                }
            }

            override fun onBatchScanResults(results: List<ScanResult>) {
                for (result in results) {
                    if (result.device.address == address) {
                        LogUtils.d(TAG, "找到目标设备: $address")
                        stopScan()
                        callback.onAddressDeviceFound(result.device)
                        return
                    }
                }
            }

            override fun onScanFailed(errorCode: Int) {
                LogUtils.e(TAG, "地址扫描失败: $errorCode")
                stopScan()
                callback.onScanFailed(errorCode)
            }
        }

        val settings = ScanSettings.Builder()
            .setLegacy(true)
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setReportDelay(0) // 立即报告
            .setUseHardwareBatchingIfSupported(false)
            .build()

        val filters = listOf(
            ScanFilter.Builder().setDeviceAddress(address).build()
        )

        startScanInternal(scanCallback, settings, filters, SCAN_TIMEOUT, ScanType.ADDRESS_SCAN) {
            isScanning = false
            currentScanType = null
            callback.onScanTimeout()
        }
    }

    /**
     * 3. OTA扫描 - 替代BluetoothWrapper.scanOTA()
     */
    fun startOTAScan(callback: ScanCallback) {
        if (checkScanConflict(ScanType.OTA_SCAN)) {
            forceStopScan()
            return
        }

        LogUtils.d(TAG, "开始OTA扫描")

        val scanCallback = object : no.nordicsemi.android.support.v18.scanner.ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                val deviceName = result.scanRecord?.deviceName
                if (!TextUtils.isEmpty(deviceName) && BleUtils.isOTADevices(deviceName)) {
                    LogUtils.d(TAG, "找到OTA设备: $deviceName")
                    stopScan()
                    callback.onOTADeviceFound(result.device)
                }
            }

            override fun onBatchScanResults(results: List<ScanResult>) {
                for (result in results) {
                    val deviceName = result.scanRecord?.deviceName
                    if (!TextUtils.isEmpty(deviceName) && BleUtils.isOTADevices(deviceName)) {
                        LogUtils.d(TAG, "找到OTA设备: $deviceName")
                        stopScan()
                        callback.onOTADeviceFound(result.device)
                        return
                    }
                }
            }

            override fun onScanFailed(errorCode: Int) {
                LogUtils.e(TAG, "OTA扫描失败: $errorCode")
                stopScan()
                callback.onScanFailed(errorCode)
            }
        }

        val settings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setReportDelay(0)
            .setUseHardwareBatchingIfSupported(false)
            .build()

        val filters = ArrayList<ScanFilter>() // 不设置过滤器，扫描所有设备

        startScanInternal(scanCallback, settings, filters, OTA_SCAN_TIMEOUT, ScanType.OTA_SCAN) {
            isScanning = false
            currentScanType = null
            callback.onScanTimeout()
        }
    }

    /**
     * 停止扫描
     */
    fun stopScan() {
        if (isScanning) {
            try {
                currentCallback?.let { scanner.stopScan(it) }
                isScanning = false
                currentCallback = null
                currentScanType = null

                timeoutRunnable?.let { handler.removeCallbacks(it) }
                timeoutRunnable = null

                LogUtils.d(TAG, "停止扫描")
            } catch (e: Exception) {
                LogUtils.e("停止扫描失败", e)
            }
        }
    }

    /**
     * 跳过扫描 - 替代BluetoothWrapper.skipScan()
     */
    fun skipScan() {
        LogUtils.d(TAG, "跳过扫描")
        stopScan()
    }

    /**
     * 强制停止当前扫描，用于优先级更高的扫描
     */
    fun forceStopScan() {
        if (isScanning) {
            LogUtils.w(TAG, "$ 强制停止当前${currentScanType}扫描")
            stopScan()
        }
    }


    /**
     * 4. 后台扫描 - 替代BluetoothManage.startBackgroundScan()
     * 使用PendingIntent进行后台扫描，用于保活和自动重连
     */
    fun startBackgroundScan(context: Context, isConnectedCheck: () -> Boolean = { false }): Boolean {
        if (!BleParams.isBluetoothJewelry(null) ||
            !PermissionUtil.hasBluetoothPermissionQuiet() ||
            !BleUtils.isBlEEnable(Utils.getApp())) {
            LogUtils.e(TAG, "无蓝牙权限或蓝牙未开启")
            return false
        }

        if (checkScanConflict(ScanType.BACKGROUND_SCAN)) {
            return false
        }

        // 检查是否已连接
        if (isConnectedCheck()) {
            LogUtils.e(TAG, "已连接, 不需要后台扫描")
            return false
        }

        // 如果已经有了后台任务, 优先移除
        if (backgroundScanPendingIntent != null) {
            stopBackgroundScan(context)
        }

        val name = PreferencesUtils.getString(context, BleParams.PAIRED_JEWELRY_NAME_TAG, "")
        val address = PreferencesUtils.getString(context, BleParams.PAIRED_BLE_ADRESS_TAG, "")

        if (TextUtils.isEmpty(name) || TextUtils.isEmpty(address)) {
            LogUtils.e(TAG, "设备名称或地址为空")
            return false
        }

        LogUtils.d(TAG, "开始后台扫描: $name - $address")

        // 智能扫描设置 - 根据电池状态和连接历史动态调整
        val settings =  ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_POWER)
            .setReportDelay(30000) // 30秒延迟，大幅降低频率
            .setMatchMode(ScanSettings.MATCH_MODE_STICKY)
            .setNumOfMatches(ScanSettings.MATCH_NUM_ONE_ADVERTISEMENT)
            .build()

        val filters = listOf(
            ScanFilter.Builder()
                .setDeviceAddress(address)
                .setDeviceName(name)
                .build()
        )

        return try {
            // 设置重连的 AlarmService
            val intent = Intent().apply {
                setClass(context, KeepAliveService::class.java)
                action = BleParams.ACTION_BLE_SCAN_NOTIFY
            }

            val pendingIntent = PendingIntent.getService(
                context,
                BLE_BACKGROUND_SCAN_REQUEST_CODE,
                intent,
                Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT)
            )

            backgroundScanPendingIntent = pendingIntent
            // 不再需要传递PendingIntent，ScanManager内部管理
            scanner.startScan(filters, settings, context, pendingIntent, BLE_BACKGROUND_SCAN_REQUEST_CODE)
            LogUtils.d(TAG, "后台扫描启动成功")
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动后台扫描失败: ${e.message}")
            false
        }
    }

    /**
     * 停止后台扫描 - 替代BluetoothManage.stopBackgroundScan()
     */
    fun stopBackgroundScan(context: Context) {
        LogUtils.w(TAG, "停止后台扫描: $backgroundScanPendingIntent")

        try {
            backgroundScanPendingIntent?.let {
                scanner.stopScan(context, it, BLE_BACKGROUND_SCAN_REQUEST_CODE)
                backgroundScanPendingIntent = null
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "停止后台扫描失败: ${e.message}")
        }
    }

    /**
     * 内部扫描启动方法
     */
    private fun startScanInternal(
        scanCallback: no.nordicsemi.android.support.v18.scanner.ScanCallback,
        settings: ScanSettings,
        filters: List<ScanFilter>,
        timeout: Long,
        scanType: ScanType,
        onTimeout: () -> Unit
    ) {
        try {
            scanner.startScan(filters, settings, scanCallback)
            isScanning = true
            currentCallback = scanCallback
            currentScanType = scanType

            LogUtils.d(TAG, "开始${scanType}扫描")

            // 设置超时
            timeoutRunnable = Runnable {
                if (isScanning) {
                    stopScan()
                    onTimeout()
                }
            }
            handler.postDelayed(timeoutRunnable!!, timeout)

        } catch (e: Exception) {
            LogUtils.e("启动扫描失败", e)
            isScanning = false
            currentCallback = null
            currentScanType = null
        }
    }

    /**
     * 过滤和处理设备 - 严格按照你的要求
     * 1. deviceName.startsWith("TWO")
     * 2. TxPowerLevel > 0
     * 3. 不能是已存在的设备
     */
    private fun filterAndProcessDevices(results: List<ScanResult>, callback: ScanCallback) {
        for (result in results) {
            val scanRecord = result.scanRecord ?: continue
            val deviceName = scanRecord.deviceName ?: continue

            // 1. 检查设备名称：必须以"TWO"开头
            if (!deviceName.startsWith(BleParams.COMMON_JEWELEY_PRE)) {
                continue
            }

            LogUtils.d(TAG, "发现设备: $deviceName, TxPowerLevel: ${scanRecord.txPowerLevel}")

            // 2. 检查信号强度：TxPowerLevel > 0
            if (scanRecord.txPowerLevel <= 0) {
                continue
            }

            // 3. 检查是否已绑定：不能重复绑定相同MAC地址的设备
            if (hasSameJewelry(result.device.address)) {
                continue
            }

            // 4. 处理厂商数据（复用原有逻辑）
            val sparseArray = scanRecord.manufacturerSpecificData
            if (sparseArray != null && sparseArray.size() > 0) {
                try {
                    val hex = Integer.toHexString(sparseArray.keyAt(0))
                    val first = hex.substring(0, 2)
                    val end = hex.substring(2)
                    val result_hex = end + first
                    val bytesTemp = sparseArray.valueAt(0)
                    val hesTemp = BleUtils.bytesToHexString(bytesTemp)

                    // 保存设备信息
                    PreferencesUtils.put(
                        Utils.getApp(),
                        BleParams.TOTWOO_DEVICE_INFO,
                        result_hex + hesTemp
                    )
                } catch (e: Exception) {
                    LogUtils.e("处理厂商数据失败", e)
                }
            }

            // 5. 找到符合条件的设备，停止扫描并回调
            stopScan()
            callback.onDeviceFound(result.device, result.rssi, scanRecord)
            return
        }
    }

    /**
     * 检查是否已绑定相同设备 - 按MAC地址判断
     */
    private fun hasSameJewelry(address: String): Boolean {
        localJewelryInfos?.let { infos ->
            for (info in infos) {
                // 同MAC地址不能重复
                if (TextUtils.equals(info.mac_address, address)) {
                    return true
                }
            }
        }
        return false
    }
}
