package com.totwoo.totwoo.ble.companion

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.companion.AssociationInfo
import android.companion.CompanionDeviceManager
import android.companion.CompanionDeviceService
import android.content.ComponentName
import android.content.Intent
import android.os.Build
import androidx.annotation.RequiresApi
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.ble.BluetoothManage

/**
 * ToTwoo配套设备服务
 * 
 * 功能：
 * 1. 监听配套设备的出现和消失
 * 2. 自动触发重连逻辑
 * 3. 利用配套设备权限启动前台服务
 * 4. 防抖和智能重连机制
 * 
 * 注意：此服务需要Android 12+ (API 31+)
 */
@RequiresApi(Build.VERSION_CODES.S)
class TotwooCompanionService : CompanionDeviceService() {
    
    companion object {
        private const val TAG = "TotwooCompanionService"
        
        // 防抖机制相关
        private const val DEBOUNCE_DELAY_MS = 3000L // 3秒防抖
        private const val MIN_RECONNECT_INTERVAL_MS = 5000L // 最小重连间隔5秒
        
        // 前台服务相关
        private const val NOTIFICATION_CHANNEL_ID = "companion_device_channel"
        private const val NOTIFICATION_ID = 1001
    }
    
    // 防抖机制状态
    private var lastDeviceAppearedTime = 0L
    private var lastDeviceDisappearedTime = 0L
    private var lastReconnectTime = 0L
    private var lastAppearedDeviceMac = ""
    private var lastDisappearedDeviceMac = ""
    
    private var hasRequestedNotificationAccess = false
    
    override fun onDeviceAppeared(associationInfo: AssociationInfo) {
        super.onDeviceAppeared(associationInfo)
        
        val deviceMac = getDeviceMacAddress(associationInfo)
        val currentTime = System.currentTimeMillis()
        
        LogUtils.d(TAG, "配套设备出现: $deviceMac")
        
        // 防抖检查：如果是同一设备在短时间内重复出现，忽略
        if (deviceMac == lastAppearedDeviceMac && 
            (currentTime - lastDeviceAppearedTime) < DEBOUNCE_DELAY_MS) {
            LogUtils.d(TAG, "设备出现事件防抖，忽略重复触发: $deviceMac")
            return
        }
        
        // 智能重连检查：避免过于频繁的重连
        val shouldReconnect = shouldTriggerReconnect(deviceMac, currentTime)
        
        lastDeviceAppearedTime = currentTime
        lastAppearedDeviceMac = deviceMac
        
        try {
            // 1. 申请后台运行权限（首次设备出现时）
            requestNotificationAccessIfNeeded()
            
            // 2. 启动前台服务（利用配套设备权限）
            startForegroundServiceIfNeeded()
            
            // 3. 智能触发自动重连
            if (shouldReconnect) {
                val bluetoothManage = BluetoothManage.getInstance()
                if (bluetoothManage != null) {
                    bluetoothManage.reconnect(false) // 自动重连，非用户触发
                    lastReconnectTime = currentTime
                    LogUtils.d(TAG, "触发智能自动重连")
                } else {
                    LogUtils.w(TAG, "BluetoothManage实例为空，跳过重连")
                }
            } else {
                LogUtils.d(TAG, "跳过重连（间隔太短或设备已连接）")
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "处理设备出现事件失败: ${e.message}")
        }
    }
    
    override fun onDeviceDisappeared(associationInfo: AssociationInfo) {
        super.onDeviceDisappeared(associationInfo)
        
        val deviceMac = getDeviceMacAddress(associationInfo)
        val currentTime = System.currentTimeMillis()
        
        // 防抖检查：如果是同一设备在短时间内重复消失，忽略
        if (deviceMac == lastDisappearedDeviceMac && 
            (currentTime - lastDeviceDisappearedTime) < DEBOUNCE_DELAY_MS) {
            LogUtils.d(TAG, "设备消失事件防抖，忽略重复触发: $deviceMac")
            return
        }
        
        lastDeviceDisappearedTime = currentTime
        lastDisappearedDeviceMac = deviceMac
        
        LogUtils.d(TAG, "配套设备消失: $deviceMac")
        
        try {
            // 设备消失时的处理逻辑
            // 可以在这里停止一些不必要的后台任务
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "处理设备消失事件失败: ${e.message}")
        }
    }
    
    /**
     * 判断是否应该触发重连
     */
    private fun shouldTriggerReconnect(deviceMac: String, currentTime: Long): Boolean {
        return try {
            // 1. 检查重连间隔
            if ((currentTime - lastReconnectTime) < MIN_RECONNECT_INTERVAL_MS) {
                LogUtils.d(TAG, "重连间隔太短，跳过重连")
                return false
            }
            
            // 2. 检查当前连接状态
            val bluetoothManage = BluetoothManage.getInstance()
            if (bluetoothManage != null) {
                // 如果已经连接，不需要重连
                if (bluetoothManage.isConnected()) {
                    LogUtils.d(TAG, "设备已连接，跳过重连")
                    return false
                }
                
                // 如果正在连接中，不需要重连
                val connectState = com.totwoo.totwoo.ble.JewInfoSingleton.getInstance().connectState
                if (connectState == com.totwoo.totwoo.ble.JewInfoSingleton.STATE_RECONNECTING) {
                    LogUtils.d(TAG, "设备正在连接中，跳过重连")
                    return false
                }
            }
            
            // 3. 其他情况允许重连
            LogUtils.d(TAG, "满足重连条件，允许触发重连")
            true
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "检查重连条件失败: ${e.message}")
            false
        }
    }
    
    /**
     * 申请后台运行权限（如果需要）
     */
    private fun requestNotificationAccessIfNeeded() {
        if (hasRequestedNotificationAccess) {
            LogUtils.d(TAG, "已申请过后台运行权限，跳过")
            return
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 使用新的API
                val companionDeviceManager = getSystemService(CompanionDeviceManager::class.java)
                if (companionDeviceManager != null) {
                    LogUtils.d(TAG, "申请CompanionDevice后台运行权限")
                    
                    // 创建ComponentName对象
                    val componentName = ComponentName(this, TotwooCompanionService::class.java)
                    companionDeviceManager.requestNotificationAccess(componentName)
                    
                    hasRequestedNotificationAccess = true
                    LogUtils.d(TAG, "后台运行权限申请已提交，系统将弹出权限对话框")
                } else {
                    LogUtils.e(TAG, "无法获取CompanionDeviceManager")
                }
            } else {
                // Android 12 可能不需要显式申请，或使用其他方式
                LogUtils.d(TAG, "Android 12 不需要显式申请后台运行权限")
                hasRequestedNotificationAccess = true
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "申请后台运行权限失败: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 启动前台服务（如果需要）
     */
    private fun startForegroundServiceIfNeeded() {
        try {
            LogUtils.d(TAG, "使用配套设备权限启动前台服务")
            
            // 创建通知渠道
            createNotificationChannel()
            
            // 创建前台服务通知
            val notification = createForegroundNotification()
            
            // 启动前台服务
            startForeground(NOTIFICATION_ID, notification)
            
            LogUtils.d(TAG, "前台服务启动成功")
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动前台服务失败: ${e.message}")
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "配套设备服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "ToTwoo配套设备后台服务"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager?.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createForegroundNotification(): Notification {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Notification.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setContentTitle("ToTwoo配套设备")
                .setContentText("正在后台保持与设备的连接")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setOngoing(true)
                .build()
        } else {
            @Suppress("DEPRECATION")
            Notification.Builder(this)
                .setContentTitle("ToTwoo配套设备")
                .setContentText("正在后台保持与设备的连接")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setOngoing(true)
                .build()
        }
    }
    
    /**
     * 获取关联ID
     */
    private fun getAssociationId(associationInfo: AssociationInfo): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                associationInfo.id
            } else {
                // Android 12 使用反射避免编译警告
                associationInfo.javaClass.getMethod("getId").invoke(associationInfo) as Int
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取关联ID失败: ${e.message}")
            -1
        }
    }
    
    /**
     * 安全获取设备MAC地址
     */
    private fun getDeviceMacAddress(associationInfo: AssociationInfo): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                associationInfo.deviceMacAddress?.toString() ?: ""
            } else {
                // Android 12 返回关联ID作为标识
                "association_${getAssociationId(associationInfo)}"
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取设备MAC地址失败: ${e.message}")
            ""
        }
    }
}
