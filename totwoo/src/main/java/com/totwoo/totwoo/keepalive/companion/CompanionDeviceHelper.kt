package com.totwoo.totwoo.keepalive.companion

import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.companion.BluetoothLeDeviceFilter
import android.bluetooth.le.ScanResult
import android.companion.AssociationInfo
import android.companion.AssociationRequest
import android.companion.CompanionDeviceManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.os.Build
import android.os.Parcelable
import androidx.annotation.RequiresApi
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.utils.PreferencesUtils
import java.util.regex.Pattern

/**
 * CompanionDevice配套设备管理器
 *
 * 功能：
 * 1. 设备配对和关联管理
 * 2. 设备监听和自动重连
 * 3. 后台运行权限管理
 * 4. 版本兼容处理 (Android 8.0+)
 *
 * 使用方式：
 * 1. 初始化：CompanionDeviceHelper.getInstance().init(context)
 * 2. 配对设备：startDevicePairing(activity, callback)
 * 3. 检查状态：isDeviceAssociated()
 */
class CompanionDeviceHelper private constructor() {

    companion object {
        private const val TAG = "CompanionDeviceHelper"
        private const val REQUEST_CODE_COMPANION_DEVICE = 1001
        private const val PREF_COMPANION_DEVICE_MAC = "companion_device_mac"

        @Volatile
        private var instance: CompanionDeviceHelper? = null

        fun getInstance(): CompanionDeviceHelper {
            return instance ?: synchronized(this) {
                instance ?: CompanionDeviceHelper().also { instance = it }
            }
        }
    }

    private var companionDeviceManager: CompanionDeviceManager? = null
    private var context: Context? = null
    private var currentCallback: CompanionDeviceCallback? = null

    /**
     * 初始化
     */
    fun init(context: Context) {
        this.context = context.applicationContext

        if (isCompanionDeviceSupported()) {
            companionDeviceManager = context.getSystemService(CompanionDeviceManager::class.java)
            LogUtils.d(TAG, "CompanionDeviceHelper初始化完成")
        } else {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
        }
    }

    /**
     * 检查是否支持CompanionDevice功能
     */
    fun isCompanionDeviceSupported(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
    }

    /**
     * 开始设备配对流程
     */
    fun startDevicePairing(activity: Activity, callback: CompanionDeviceCallback) {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            callback.onError("系统版本过低，不支持配套设备功能")
            return
        }

        companionDeviceManager?.let { manager ->
            currentCallback = callback

            try {
                LogUtils.d(TAG, "准备创建配对请求，Android版本: ${Build.VERSION.SDK_INT}")

                // 检查是否已有配对的传统蓝牙设备
                val pairedDeviceMac = getExistingPairedDeviceMac()
                LogUtils.d(TAG, "已配对的传统蓝牙设备MAC: $pairedDeviceMac")

                // 创建蓝牙LE设备过滤器
                val filterBuilder = BluetoothLeDeviceFilter.Builder()

                if (pairedDeviceMac.isNotEmpty()) {
                    LogUtils.d(TAG, "使用已配对设备MAC创建过滤器: $pairedDeviceMac")
                    filterBuilder.setNamePattern(
                        Pattern.compile(
                            "ToTwoo.*|TOTWOO.*|TT.*",
                            Pattern.CASE_INSENSITIVE
                        )
                    )
                } else {
                    LogUtils.d(TAG, "使用通用名称过滤器")
                    filterBuilder.setNamePattern(
                        Pattern.compile(
                            "ToTwoo.*|TOTWOO.*|TT.*",
                            Pattern.CASE_INSENSITIVE
                        )
                    )
                }

                val deviceFilter = filterBuilder.build()

                // 创建配对请求
                val requestBuilder = AssociationRequest.Builder()
                    .addDeviceFilter(deviceFilter)

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    requestBuilder.setSingleDevice(false) // 允许显示多个设备供选择
                } else {
                    requestBuilder.setSingleDevice(true)
                }

                val pairingRequest = requestBuilder.build()

                LogUtils.d(TAG, "开始配套设备配对流程，过滤器已配置")

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    startPairingForAndroid13Plus(activity, pairingRequest)
                } else {
                    startPairingForAndroid8To12(activity, pairingRequest)
                }

            } catch (e: Exception) {
                LogUtils.e(TAG, "启动配对流程失败: ${e.message}")
                e.printStackTrace()
                callback.onError("启动配对失败: ${e.message}")
            }
        } ?: run {
            LogUtils.e(TAG, "CompanionDeviceManager未初始化")
            callback.onError("配套设备管理器未初始化")
        }
    }

    /**
     * 基于已配对的传统蓝牙设备自动进行CompanionDevice配对
     */
    fun autoAssociateWithPairedDevice(
        activity: Activity,
        deviceName: String,
        deviceMac: String,
        callback: CompanionDeviceCallback
    ) {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            callback.onError("系统版本过低，不支持配套设备功能")
            return
        }

        if (deviceName.isEmpty() && deviceMac.isEmpty()) {
            LogUtils.e(TAG, "设备名称和MAC地址都为空，无法进行自动配对")
            callback.onError("设备信息不完整，无法自动配对")
            return
        }

        // 检查是否已经关联
        if (isDeviceAssociated()) {
            val existingMac = getAssociatedDeviceMac()
            if (deviceMac == existingMac) {
                LogUtils.d(TAG, "设备已经关联，无需重复配对: $deviceMac")
                callback.onDeviceAssociated(deviceMac)
                return
            }
        }

        LogUtils.d(TAG, "开始基于已配对设备的自动CompanionDevice配对")
        LogUtils.d(TAG, "设备名称: $deviceName, MAC: $deviceMac")

        currentCallback = callback

        try {
            // 创建精确匹配的设备过滤器
            val filterBuilder = BluetoothLeDeviceFilter.Builder()

            // 优先使用设备名称过滤
            if (deviceName.isNotEmpty()) {
                val exactPattern = Pattern.quote(deviceName)
                val fuzzyPattern = deviceName.replace("\\s+".toRegex(), ".*")
                val combinedPattern = "($exactPattern|$fuzzyPattern)"
                filterBuilder.setNamePattern(
                    Pattern.compile(
                        combinedPattern,
                        Pattern.CASE_INSENSITIVE
                    )
                )
                LogUtils.d(TAG, "使用设备名称过滤器: $combinedPattern")
            }

            val deviceFilter = filterBuilder.build()

            // 创建配对请求
            val requestBuilder = AssociationRequest.Builder()
                .addDeviceFilter(deviceFilter)

            // 对于自动配对，尝试单设备模式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                requestBuilder.setSingleDevice(true)
            }

            val pairingRequest = requestBuilder.build()

            LogUtils.d(TAG, "自动配对请求已创建，准备启动")

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                startAutoAssociationForAndroid13Plus(activity, pairingRequest)
            } else {
                startAutoAssociationForAndroid8To12(activity, pairingRequest)
            }

        } catch (e: Exception) {
            LogUtils.e(TAG, "自动配对失败: ${e.message}")
            e.printStackTrace()
            callback.onError("自动配对失败: ${e.message}")
        }
    }

    /**
     * 简化配对方法（用于调试，不使用设备过滤器）
     */
    fun startSimplePairing(activity: Activity, callback: CompanionDeviceCallback) {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            callback.onError("系统版本过低，不支持配套设备功能")
            return
        }

        companionDeviceManager?.let { manager ->
            currentCallback = callback

            try {
                LogUtils.d(TAG, "开始简化配对流程（无过滤器），Android版本: ${Build.VERSION.SDK_INT}")

                // 创建不带过滤器的配对请求，显示所有可用设备
                val pairingRequest = AssociationRequest.Builder().build()

                LogUtils.d(TAG, "简化配对请求已创建，准备启动配对界面")

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    startSimplePairingForAndroid13Plus(activity, pairingRequest)
                } else {
                    startSimplePairingForAndroid8To12(activity, pairingRequest)
                }

            } catch (e: Exception) {
                LogUtils.e(TAG, "启动简化配对流程失败: ${e.message}")
                e.printStackTrace()
                callback.onError("启动简化配对失败: ${e.message}")
            }
        } ?: run {
            LogUtils.e(TAG, "CompanionDeviceManager未初始化")
            callback.onError("配套设备管理器未初始化")
        }
    }

    /**
     * 申请后台运行权限
     */
    fun requestNotificationAccess(context: Context) {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            return
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                companionDeviceManager?.let { manager ->
                    LogUtils.d(TAG, "申请CompanionDevice后台运行权限")

                    val componentName = ComponentName(context, TotwooCompanionService::class.java)
                    manager.requestNotificationAccess(componentName)

                    LogUtils.d(TAG, "后台运行权限申请已提交")
                } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
            } else {
                LogUtils.d(TAG, "Android 12 不需要显式申请后台运行权限")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "申请后台运行权限失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 检查后台权限状态
     */
    fun checkNotificationAccessStatus(context: Context): String {
        val status = StringBuilder()

        try {
            status.appendLine("=== 后台权限状态 ===")
            status.appendLine("Android版本: ${Build.VERSION.SDK_INT}")

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                status.appendLine("需要显式申请: 是")

                companionDeviceManager?.let { manager ->
                    status.appendLine("CompanionDeviceManager: 可用")

                    try {
                        val associations = manager.associations
                        status.appendLine("关联设备数量: ${associations.size}")
                        associations.forEachIndexed { index, association ->
                            status.appendLine("设备${index + 1}: $association")
                        }
                    } catch (e: Exception) {
                        status.appendLine("获取关联设备失败: ${e.message}")
                    }
                } ?: status.appendLine("CompanionDeviceManager: 不可用")
            } else {
                status.appendLine("需要显式申请: 否")
                status.appendLine("Android 12 可能自动获得权限")
            }

        } catch (e: Exception) {
            status.appendLine("检查权限状态失败: ${e.message}")
        }

        return status.toString()
    }

    /**
     * 配对回调接口
     */
    interface CompanionDeviceCallback {
        fun onDeviceAssociated(deviceMac: String)
        fun onError(error: String)
    }

    // ========== 私有方法 ==========

    /**
     * 获取已配对的传统蓝牙设备MAC地址
     */
    private fun getExistingPairedDeviceMac(): String {
        return try {
            context?.let { ctx ->
                PreferencesUtils.getString(
                    ctx,
                    com.totwoo.totwoo.ble.BleParams.PAIRED_BLE_ADRESS_TAG,
                    ""
                )
            } ?: ""
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取已配对设备MAC失败: ${e.message}")
            ""
        }
    }

    /**
     * Android 13+ 配对处理
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun startPairingForAndroid13Plus(activity: Activity, request: AssociationRequest) {
        val executor = activity.mainExecutor
        LogUtils.d(TAG, "使用Android 13+配对API")

        companionDeviceManager?.associate(
            request,
            executor,
            object : CompanionDeviceManager.Callback() {
                override fun onAssociationPending(intentSender: IntentSender) {
                    LogUtils.d(TAG, "Android 13+ 配对界面准备显示")
                    try {
                        activity.startIntentSenderForResult(
                            intentSender,
                            REQUEST_CODE_COMPANION_DEVICE, null, 0, 0, 0
                        )
                        LogUtils.d(TAG, "配对界面启动成功")
                    } catch (e: Exception) {
                        LogUtils.e(TAG, "启动配对界面失败: ${e.message}")
                        e.printStackTrace()
                        currentCallback?.onError("启动配对界面失败: ${e.message}")
                    }
                }

                override fun onAssociationCreated(associationInfo: AssociationInfo) {
                    val associationId = getAssociationId(associationInfo)
                    LogUtils.d(TAG, "配对成功，关联ID: $associationId")
                    saveAssociationInfo(associationInfo)

                    val deviceMac = getDeviceMacAddress(associationInfo)
                    currentCallback?.onDeviceAssociated(deviceMac)
                }

                override fun onFailure(error: CharSequence?) {
                    LogUtils.e(TAG, "配对失败: $error")
                    currentCallback?.onError(error?.toString() ?: "配对失败")
                }
            })
    }

    /**
     * Android 8-12 配对处理
     */
    @RequiresApi(Build.VERSION_CODES.O)
    @Suppress("DEPRECATION")
    private fun startPairingForAndroid8To12(activity: Activity, request: AssociationRequest) {
        LogUtils.d(TAG, "使用Android 8-12配对API")

        companionDeviceManager?.associate(request, object : CompanionDeviceManager.Callback() {
            override fun onDeviceFound(chooserLauncher: IntentSender) {
                LogUtils.d(TAG, "Android 8-12 发现设备，准备显示选择界面")
                try {
                    activity.startIntentSenderForResult(
                        chooserLauncher,
                        REQUEST_CODE_COMPANION_DEVICE, null, 0, 0, 0
                    )
                    LogUtils.d(TAG, "设备选择界面启动成功")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "启动配对界面失败: ${e.message}")
                    e.printStackTrace()
                    currentCallback?.onError("启动配对界面失败: ${e.message}")
                }
            }

            override fun onFailure(error: CharSequence?) {
                LogUtils.e(TAG, "配对失败: $error")
                currentCallback?.onError(error?.toString() ?: "配对失败")
            }
        }, null)
    }

    /**
     * 自动配对 - Android 13+
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun startAutoAssociationForAndroid13Plus(
        activity: Activity,
        request: AssociationRequest
    ) {
        val executor = activity.mainExecutor
        LogUtils.d(TAG, "使用Android 13+自动配对API")

        companionDeviceManager?.associate(
            request,
            executor,
            object : CompanionDeviceManager.Callback() {
                override fun onAssociationPending(intentSender: IntentSender) {
                    LogUtils.d(TAG, "自动配对界面准备显示（Android 13+）")
                    try {
                        activity.startIntentSenderForResult(
                            intentSender,
                            REQUEST_CODE_COMPANION_DEVICE, null, 0, 0, 0
                        )
                        LogUtils.d(TAG, "自动配对界面启动成功")
                    } catch (e: Exception) {
                        LogUtils.e(TAG, "启动自动配对界面失败: ${e.message}")
                        e.printStackTrace()
                        currentCallback?.onError("启动配对界面失败: ${e.message}")
                    }
                }

                override fun onAssociationCreated(associationInfo: AssociationInfo) {
                    val associationId = getAssociationId(associationInfo)
                    LogUtils.d(TAG, "自动配对成功，关联ID: $associationId")
                    saveAssociationInfo(associationInfo)

                    // 自动启动设备监听
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        startObservingDevicePresence()
                        LogUtils.d(TAG, "自动配对成功后已启动设备监听")
                    }

                    val deviceMac = getDeviceMacAddress(associationInfo)
                    currentCallback?.onDeviceAssociated(deviceMac)
                }

                override fun onFailure(error: CharSequence?) {
                    LogUtils.e(TAG, "自动配对失败: $error")
                    currentCallback?.onError(error?.toString() ?: "自动配对失败")
                }
            })
    }

    /**
     * 自动配对 - Android 8-12
     */
    @RequiresApi(Build.VERSION_CODES.O)
    @Suppress("DEPRECATION")
    private fun startAutoAssociationForAndroid8To12(
        activity: Activity,
        request: AssociationRequest
    ) {
        LogUtils.d(TAG, "使用Android 8-12自动配对API")

        companionDeviceManager?.associate(request, object : CompanionDeviceManager.Callback() {
            override fun onDeviceFound(chooserLauncher: IntentSender) {
                LogUtils.d(TAG, "自动配对发现设备，准备显示选择界面（Android 8-12）")
                try {
                    activity.startIntentSenderForResult(
                        chooserLauncher,
                        REQUEST_CODE_COMPANION_DEVICE, null, 0, 0, 0
                    )
                    LogUtils.d(TAG, "自动配对设备选择界面启动成功")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "启动自动配对界面失败: ${e.message}")
                    e.printStackTrace()
                    currentCallback?.onError("启动配对界面失败: ${e.message}")
                }
            }

            override fun onFailure(error: CharSequence?) {
                LogUtils.e(TAG, "自动配对失败: $error")
                currentCallback?.onError(error?.toString() ?: "自动配对失败")
            }
        }, null)
    }

    /**
     * 简化配对 - Android 13+
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun startSimplePairingForAndroid13Plus(
        activity: Activity,
        request: AssociationRequest
    ) {
        val executor = activity.mainExecutor
        LogUtils.d(TAG, "使用Android 13+简化配对API")

        companionDeviceManager?.associate(
            request,
            executor,
            object : CompanionDeviceManager.Callback() {
                override fun onAssociationPending(intentSender: IntentSender) {
                    LogUtils.d(TAG, "简化配对界面准备显示（Android 13+）")
                    try {
                        activity.startIntentSenderForResult(
                            intentSender,
                            REQUEST_CODE_COMPANION_DEVICE, null, 0, 0, 0
                        )
                        LogUtils.d(TAG, "简化配对界面启动成功")
                    } catch (e: Exception) {
                        LogUtils.e(TAG, "启动简化配对界面失败: ${e.message}")
                        e.printStackTrace()
                        currentCallback?.onError("启动配对界面失败: ${e.message}")
                    }
                }

                override fun onAssociationCreated(associationInfo: AssociationInfo) {
                    val associationId = getAssociationId(associationInfo)
                    LogUtils.d(TAG, "简化配对成功，关联ID: $associationId")
                    saveAssociationInfo(associationInfo)

                    val deviceMac = getDeviceMacAddress(associationInfo)
                    currentCallback?.onDeviceAssociated(deviceMac)
                }

                override fun onFailure(error: CharSequence?) {
                    LogUtils.e(TAG, "简化配对失败: $error")
                    currentCallback?.onError(error?.toString() ?: "简化配对失败")
                }
            })
    }

    /**
     * 简化配对 - Android 8-12
     */
    @RequiresApi(Build.VERSION_CODES.O)
    @Suppress("DEPRECATION")
    private fun startSimplePairingForAndroid8To12(activity: Activity, request: AssociationRequest) {
        LogUtils.d(TAG, "使用Android 8-12简化配对API")

        companionDeviceManager?.associate(request, object : CompanionDeviceManager.Callback() {
            override fun onDeviceFound(chooserLauncher: IntentSender) {
                LogUtils.d(TAG, "简化配对发现设备，准备显示选择界面（Android 8-12）")
                try {
                    activity.startIntentSenderForResult(
                        chooserLauncher,
                        REQUEST_CODE_COMPANION_DEVICE, null, 0, 0, 0
                    )
                    LogUtils.d(TAG, "简化配对设备选择界面启动成功")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "启动简化配对界面失败: ${e.message}")
                    e.printStackTrace()
                    currentCallback?.onError("启动配对界面失败: ${e.message}")
                }
            }

            override fun onFailure(error: CharSequence?) {
                LogUtils.e(TAG, "简化配对失败: $error")
                currentCallback?.onError(error?.toString() ?: "简化配对失败")
            }
        }, null)
    }

    /**
     * 处理配对结果
     */
    fun handlePairingResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode != REQUEST_CODE_COMPANION_DEVICE) {
            return
        }

        if (resultCode == Activity.RESULT_OK && data != null) {
            LogUtils.d(TAG, "配对结果处理开始，resultCode: $resultCode")

            var deviceAddress: String? = null

            try {
                // 尝试获取BluetoothDevice（旧版本API）
                val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    data.getParcelableExtra(CompanionDeviceManager.EXTRA_DEVICE, BluetoothDevice::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    data.getParcelableExtra<BluetoothDevice>(CompanionDeviceManager.EXTRA_DEVICE)
                }

                if (device != null) {
                    deviceAddress = device.address
                    LogUtils.d(TAG, "从BluetoothDevice获取地址: $deviceAddress")
                } else {
                    // 尝试获取ScanResult（新版本API）
                    val scanResult = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        data.getParcelableExtra(CompanionDeviceManager.EXTRA_DEVICE, ScanResult::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        data.getParcelableExtra<ScanResult>(CompanionDeviceManager.EXTRA_DEVICE)
                    }

                    if (scanResult?.device != null) {
                        deviceAddress = scanResult.device.address
                        LogUtils.d(TAG, "从ScanResult获取地址: $deviceAddress")
                    }
                }

            } catch (e: ClassCastException) {
                LogUtils.e(TAG, "类型转换异常，尝试其他方式获取设备信息: ${e.message}")

                try {
                    val deviceObj = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        data.getParcelableExtra(CompanionDeviceManager.EXTRA_DEVICE, Parcelable::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        data.getParcelableExtra<Parcelable>(CompanionDeviceManager.EXTRA_DEVICE)
                    }
                    deviceObj?.let { obj ->
                        LogUtils.d(TAG, "设备对象类型: ${obj.javaClass.name}")

                        when (obj) {
                            is ScanResult -> {
                                deviceAddress = obj.device?.address
                                LogUtils.d(TAG, "从ScanResult对象获取地址: $deviceAddress")
                            }

                            is BluetoothDevice -> {
                                deviceAddress = obj.address
                                LogUtils.d(TAG, "从BluetoothDevice对象获取地址: $deviceAddress")
                            }
                        }
                    }
                } catch (ex: Exception) {
                    LogUtils.e(TAG, "获取设备信息失败: ${ex.message}")
                }
            }

            if (!deviceAddress.isNullOrEmpty()) {
                LogUtils.d(TAG, "配对成功，设备地址: $deviceAddress")
                saveDeviceInfo(deviceAddress)
                currentCallback?.onDeviceAssociated(deviceAddress)
            } else {
                LogUtils.e(TAG, "无法获取设备地址")
                currentCallback?.onError("无法获取设备信息")
            }
        } else {
            LogUtils.w(TAG, "用户取消了配对或配对失败，resultCode: $resultCode")
            currentCallback?.onError("用户取消配对")
        }

        currentCallback = null
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否已有关联设备
     */
    fun isDeviceAssociated(): Boolean {
        if (!isCompanionDeviceSupported()) {
            return false
        }

        context?.let { ctx ->
            val savedMac = PreferencesUtils.getString(ctx, PREF_COMPANION_DEVICE_MAC, "")
            return savedMac.isNotEmpty()
        }
        return false
    }

    /**
     * 获取已关联的设备MAC地址
     */
    fun getAssociatedDeviceMac(): String {
        return context?.let { ctx ->
            PreferencesUtils.getString(ctx, PREF_COMPANION_DEVICE_MAC, "")
        } ?: ""
    }

    /**
     * 保存设备信息
     */
    private fun saveDeviceInfo(deviceAddress: String) {
        context?.let { ctx ->
            PreferencesUtils.put(ctx, PREF_COMPANION_DEVICE_MAC, deviceAddress)
            LogUtils.d(TAG, "保存设备MAC地址: $deviceAddress")
        }
    }

    /**
     * 保存关联信息
     */
    private fun saveAssociationInfo(associationInfo: AssociationInfo) {
        try {
            val deviceMac = getDeviceMacAddress(associationInfo)
            saveDeviceInfo(deviceMac)
        } catch (e: Exception) {
            LogUtils.e(TAG, "保存关联信息失败: ${e.message}")
        }
    }

    /**
     * 获取关联ID
     */
    private fun getAssociationId(associationInfo: AssociationInfo): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                associationInfo.id
            } else {
                // Android 12 使用反射避免编译警告
                associationInfo.javaClass.getMethod("getId").invoke(associationInfo) as Int
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取关联ID失败: ${e.message}")
            -1
        }
    }

    /**
     * 安全获取设备MAC地址
     */
    private fun getDeviceMacAddress(associationInfo: AssociationInfo): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                associationInfo.deviceMacAddress?.toString() ?: ""
            } else {
                // Android 12 返回关联ID作为标识
                "association_${getAssociationId(associationInfo)}"
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取设备MAC地址失败: ${e.message}")
            ""
        }
    }

    /**
     * 开始监听设备存在状态
     * 需要 Android 12+ (API 31+) 支持
     */
    fun startObservingDevicePresence() {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            return
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            LogUtils.w(TAG, "设备监听功能需要Android 12+")
            return
        }

        try {
            companionDeviceManager?.let { manager ->
                val associations = manager.myAssociations
                if (associations.isEmpty()) {
                    LogUtils.w(TAG, "没有找到配套设备关联，无法开始监听")
                    return
                }

                LogUtils.d(TAG, "开始监听配套设备存在状态，关联数量: ${associations.size}")

                // Android 12+ 使用 startObservingDevicePresence (需要设备MAC地址)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    for (association in associations) {
                        val deviceMac = getDeviceMacAddress(association)
                        LogUtils.d(TAG, "开始监听关联设备: $deviceMac")
                        if (deviceMac.isNotEmpty()) {
                            manager.startObservingDevicePresence(deviceMac)
                        }
                    }
                    LogUtils.d(TAG, "设备存在状态监听已启动")
                }
            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "开始监听设备存在状态失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 停止监听设备存在状态
     */
    fun stopObservingDevicePresence() {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            return
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            LogUtils.w(TAG, "设备监听功能需要Android 12+")
            return
        }

        try {
            companionDeviceManager?.let { manager ->
                val associations = manager.myAssociations
                if (associations.isEmpty()) {
                    LogUtils.d(TAG, "没有配套设备关联，无需停止监听")
                    return
                }

                LogUtils.d(TAG, "停止监听配套设备存在状态")

                // Android 12+ 使用 stopObservingDevicePresence (需要设备MAC地址)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    for (association in associations) {
                        val deviceMac = getDeviceMacAddress(association)
                        LogUtils.d(TAG, "停止监听关联设备: $deviceMac")
                        if (deviceMac.isNotEmpty()) {
                            manager.stopObservingDevicePresence(deviceMac)
                        }
                    }
                    LogUtils.d(TAG, "设备存在状态监听已停止")
                }
            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "停止监听设备存在状态失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 取消配套设备关联
     */
    fun disassociateDevice() {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            return
        }

        try {
            companionDeviceManager?.let { manager ->
                val associations = manager.myAssociations
                if (associations.isEmpty()) {
                    LogUtils.d(TAG, "没有配套设备关联，无需取消")
                    return
                }

                LogUtils.d(TAG, "开始取消配套设备关联，关联数量: ${associations.size}")

                for (association in associations) {
                    val associationId = getAssociationId(association)
                    val deviceMac = getDeviceMacAddress(association)

                    LogUtils.d(TAG, "取消关联设备: ID=$associationId, MAC=$deviceMac")

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        // Android 13+ 使用新API
                        manager.disassociate(associationId)
                    } else {
                        // Android 8-12 使用旧API
                        @Suppress("DEPRECATION")
                        manager.disassociate(deviceMac)
                    }
                }

                // 清除本地保存的关联信息
                clearAssociationInfo()
                LogUtils.d(TAG, "配套设备关联已全部取消")

            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "取消配套设备关联失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 清除本地保存的关联信息
     */
    private fun clearAssociationInfo() {
        try {
            context?.let { ctx ->
                PreferencesUtils.put(ctx, PREF_COMPANION_DEVICE_MAC, "")
                LogUtils.d(TAG, "本地关联信息已清除")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "清除本地关联信息失败: ${e.message}")
        }
    }
}
