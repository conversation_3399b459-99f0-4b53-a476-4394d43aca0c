package com.totwoo.totwoo.keepalive.companion

import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.companion.AssociationInfo
import android.companion.AssociationRequest
import android.companion.BluetoothDeviceFilter
import android.companion.CompanionDeviceManager
import android.content.Context
import android.content.IntentSender
import android.os.Build
import androidx.annotation.RequiresApi
import com.blankj.utilcode.util.Utils
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.utils.PreferencesUtils
import java.util.regex.Pattern

/**
 * CompanionDevice配套设备管理器 - 单例模式
 *
 * 核心功能：
 * 1. 基于已配对设备的自动关联 - autoAssociateWithPairedDevice()
 * 2. 申请后台运行权限 - requestNotificationAccess()
 * 3. 设备存在状态监听 - startObservingDevicePresence()
 * 4. 停止设备监听 - stopObservingDevicePresence()
 * 5. 解除设备关联 - disassociateDevice()
 *
 * 使用方式：
 * CompanionDeviceHelper.init(context)
 * CompanionDeviceHelper.autoAssociateWithPairedDevice(activity, deviceName, deviceMac, callback)
 */
object CompanionDeviceHelper {

    private const val TAG = "CompanionDeviceHelper"
    private const val REQUEST_CODE_COMPANION_DEVICE = 1001
    private const val PREF_COMPANION_DEVICE_MAC = "companion_device_mac"

    // 私有变量
    private var companionDeviceManager: CompanionDeviceManager? = null
    private var currentCallback: CompanionDeviceCallback? = null

    /**
     * 初始化CompanionDevice管理器
     */
    fun init() {
        if (isCompanionDeviceSupported()) {
            companionDeviceManager = Utils.getApp().getSystemService(CompanionDeviceManager::class.java)
            LogUtils.d(TAG, "CompanionDeviceHelper初始化完成")
        } else {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
        }
    }

    /**
     * 检查是否支持CompanionDevice功能
     */
    fun isCompanionDeviceSupported(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
    }

    /**
     * 检查设备是否已关联
     */
    fun isDeviceAssociated(): Boolean {
        if (!isCompanionDeviceSupported()) return false
        
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                companionDeviceManager?.myAssociations?.isNotEmpty() == true
            } else {
                false
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "检查设备关联状态失败: ${e.message}")
            false
        }
    }

    /**
     * 基于已配对设备自动关联配套设备
     * @param activity 当前Activity
     * @param deviceName 设备名称（可能重复，仅作参考）
     * @param deviceMac 设备MAC地址（唯一标识）
     * @param callback 回调接口
     */
    fun autoAssociateWithPairedDevice(
        activity: Activity,
        deviceName: String,
        deviceMac: String,
        callback: CompanionDeviceCallback
    ) {
        if (!isCompanionDeviceSupported()) {
            callback.onError("当前系统版本不支持CompanionDevice API")
            return
        }

        if (deviceMac.isEmpty()) {
            callback.onError("设备MAC地址不能为空")
            return
        }

        currentCallback = callback
        LogUtils.d(TAG, "开始自动关联配套设备: $deviceName ($deviceMac)")

        try {
            // 检查设备是否已配对
            val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
            val pairedDevices = bluetoothAdapter?.bondedDevices
            val targetDevice = pairedDevices?.find { it.address.equals(deviceMac, ignoreCase = true) }

            if (targetDevice == null) {
                callback.onError("设备未配对，请先进行蓝牙配对")
                return
            }

            // 检查是否已经关联
            if (isDeviceAssociated()) {
                LogUtils.d(TAG, "设备已关联，直接启动监听")
                saveAssociationInfo(deviceMac)
                callback.onDeviceAssociated(deviceMac)
                return
            }

            // 创建关联请求
            val deviceFilter = BluetoothDeviceFilter.Builder()
                .setAddress(deviceMac)
                .build()

            val associationRequest = AssociationRequest.Builder()
                .addDeviceFilter(deviceFilter)
                .setSingleDevice(true)
                .build()

            // 发起关联请求
            companionDeviceManager?.associate(
                associationRequest,
                object : CompanionDeviceManager.Callback() {
                    override fun onDeviceFound(chooserLauncher: IntentSender) {
                        LogUtils.d(TAG, "找到配套设备，启动选择器")
                        try {
                            activity.startIntentSenderForResult(
                                chooserLauncher,
                                REQUEST_CODE_COMPANION_DEVICE,
                                null, 0, 0, 0
                            )
                        } catch (e: Exception) {
                            LogUtils.e(TAG, "启动设备选择器失败: ${e.message}")
                            callback.onError("启动设备选择器失败: ${e.message}")
                        }
                    }

                    override fun onFailure(error: CharSequence?) {
                        LogUtils.e(TAG, "配套设备关联失败: $error")
                        callback.onError("配套设备关联失败: $error")
                    }
                },
                null
            )

        } catch (e: Exception) {
            LogUtils.e(TAG, "自动关联配套设备失败: ${e.message}")
            callback.onError("自动关联配套设备失败: ${e.message}")
        }
    }

    /**
     * 申请后台运行权限（通知访问权限）
     */
    fun requestNotificationAccess(context: Context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Android 12+ 自动获得后台运行权限
                LogUtils.d(TAG, "Android 12+ 配套设备自动获得后台权限")
            } else {
                LogUtils.d(TAG, "Android 11及以下版本需要手动申请通知权限")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "申请后台运行权限失败: ${e.message}")
        }
    }

    /**
     * 开始监听设备存在状态
     * 需要 Android 12+ (API 31+) 支持
     */
    @RequiresApi(Build.VERSION_CODES.S)
    fun startObservingDevicePresence() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            LogUtils.w(TAG, "设备监听功能需要Android 12+")
            return
        }

        try {
            companionDeviceManager?.let { manager ->
                val associations = manager.myAssociations
                if (associations.isEmpty()) {
                    LogUtils.w(TAG, "没有找到配套设备关联，无法开始监听")
                    return
                }

                LogUtils.d(TAG, "开始监听配套设备存在状态，关联数量: ${associations.size}")
                
                for (association: AssociationInfo in associations) {
                    val deviceMac = getDeviceMacAddress(association)
                    LogUtils.d(TAG, "开始监听关联设备: $deviceMac")
                    if (deviceMac.isNotEmpty()) {
                        manager.startObservingDevicePresence(deviceMac)
                    }
                }
                LogUtils.d(TAG, "设备存在状态监听已启动")
                
            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "开始监听设备存在状态失败: ${e.message}")
        }
    }

    /**
     * 停止监听设备存在状态
     */
    @RequiresApi(Build.VERSION_CODES.S)
    fun stopObservingDevicePresence() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            LogUtils.w(TAG, "设备监听功能需要Android 12+")
            return
        }

        try {
            companionDeviceManager?.let { manager ->
                val associations = manager.myAssociations
                if (associations.isEmpty()) {
                    LogUtils.d(TAG, "没有配套设备关联，无需停止监听")
                    return
                }

                LogUtils.d(TAG, "停止监听配套设备存在状态")
                
                for (association: AssociationInfo in associations) {
                    val deviceMac = getDeviceMacAddress(association)
                    LogUtils.d(TAG, "停止监听关联设备: $deviceMac")
                    if (deviceMac.isNotEmpty()) {
                        manager.stopObservingDevicePresence(deviceMac)
                    }
                }
                LogUtils.d(TAG, "设备存在状态监听已停止")
                
            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "停止监听设备存在状态失败: ${e.message}")
        }
    }

    /**
     * 取消配套设备关联
     */
    fun disassociateDevice() {
        if (!isCompanionDeviceSupported()) {
            LogUtils.w(TAG, "当前系统版本不支持CompanionDevice API")
            return
        }

        try {
            companionDeviceManager?.let { manager ->
                val associations = manager.myAssociations
                if (associations.isEmpty()) {
                    LogUtils.d(TAG, "没有配套设备关联，无需取消")
                    return
                }

                LogUtils.d(TAG, "开始取消配套设备关联，关联数量: ${associations.size}")
                
                for (association: AssociationInfo in associations) {
                    val deviceMac = getDeviceMacAddress(association)
                    LogUtils.d(TAG, "取消关联设备: MAC=$deviceMac")

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        // Android 13+ 使用新API
                        val associationId = getAssociationId(association)
                        manager.disassociate(associationId)
                    } else {
                        // Android 8-12 使用旧API
                        @Suppress("DEPRECATION")
                        manager.disassociate(deviceMac)
                    }
                }
                
                // 清除本地保存的关联信息
                clearAssociationInfo()
                LogUtils.d(TAG, "配套设备关联已全部取消")
                
            } ?: LogUtils.e(TAG, "CompanionDeviceManager未初始化")
        } catch (e: Exception) {
            LogUtils.e(TAG, "取消配套设备关联失败: ${e.message}")
        }
    }

    /**
     * 处理关联结果
     */
    fun handleAssociationResult(resultCode: Int, deviceMac: String) {
        if (resultCode == Activity.RESULT_OK) {
            LogUtils.d(TAG, "配套设备关联成功: $deviceMac")
            saveAssociationInfo(deviceMac)
            currentCallback?.onDeviceAssociated(deviceMac)
        } else {
            LogUtils.w(TAG, "配套设备关联被取消")
            currentCallback?.onError("用户取消了设备关联")
        }
        currentCallback = null
    }

    // 私有辅助方法
    private fun getDeviceMacAddress(associationInfo: AssociationInfo): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                associationInfo.deviceMacAddress?.toString() ?: ""
            } else {
                // Android 8-12 使用反射获取MAC地址
                val macField = associationInfo.javaClass.getDeclaredField("mDeviceMacAddress")
                macField.isAccessible = true
                macField.get(associationInfo)?.toString() ?: ""
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取设备MAC地址失败: ${e.message}")
            ""
        }
    }

    private fun getAssociationId(associationInfo: AssociationInfo): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                associationInfo.id
            } else {
                // Android 12 使用反射获取ID
                val idField = associationInfo.javaClass.getDeclaredField("mId")
                idField.isAccessible = true
                idField.getInt(associationInfo)
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "获取关联ID失败: ${e.message}")
            -1
        }
    }

    private fun saveAssociationInfo(deviceMac: String) {
        try {
            context?.let { ctx ->
                PreferencesUtils.put(ctx, PREF_COMPANION_DEVICE_MAC, deviceMac)
                LogUtils.d(TAG, "保存关联信息: $deviceMac")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "保存关联信息失败: ${e.message}")
        }
    }

    private fun clearAssociationInfo() {
        try {
            context?.let { ctx ->
                PreferencesUtils.put(ctx, PREF_COMPANION_DEVICE_MAC, "")
                LogUtils.d(TAG, "本地关联信息已清除")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "清除本地关联信息失败: ${e.message}")
        }
    }

    /**
     * 配对回调接口
     */
    interface CompanionDeviceCallback {
        fun onDeviceAssociated(deviceMac: String)
        fun onError(error: String)
    }
}
